/* CustomScrollbar CSS */
.custom-scrollbar-container {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

/* Content container with hidden native scrollbar */
.custom-scrollbar-content {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  overflow-x: hidden;

  /* Hide native scrollbar in different browsers */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.custom-scrollbar-content::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Edge */
}

/* Custom scrollbar track */
.custom-scrollbar-track {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  background: var(--scrollbar-track-color, transparent);
  transition: width var(--scrollbar-animation-duration, 300ms) ease-in-out;
  z-index: 10;
  border-radius: 10px;
}

/* Custom scrollbar thumb */
.custom-scrollbar-thumb {
  position: absolute;
  right: 0;
  width: 100%;
  background: var(--scrollbar-thumb-color, rgba(100, 100, 100, 0.5));
  border-radius: 10px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all var(--scrollbar-animation-duration, 300ms) ease-in-out;
  cursor: pointer;
  min-height: 20px;
}

.custom-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover-color, rgba(100, 100, 100, 0.8));
}

/* Thumb hover state with enhanced effects */
.custom-scrollbar-thumb.thumb-hovered {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  background: var(--scrollbar-thumb-hover-color, rgba(100, 100, 100, 0.8)) !important;
}

/* Thumb dragging state */
.custom-scrollbar-thumb.thumb-dragging {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  background: var(--scrollbar-thumb-hover-color, rgba(100, 100, 100, 0.9)) !important;
}

/* Dark mode support */
.dark .custom-scrollbar-thumb {
  background: rgba(200, 200, 200, 0.3);
}

.dark .custom-scrollbar-thumb:hover {
  background: rgba(200, 200, 200, 0.5);
}

/* Dark mode thumb hover state */
.dark .custom-scrollbar-thumb.thumb-hovered {
  background: rgba(200, 200, 200, 0.6) !important;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
}

/* Dark mode thumb dragging state */
.dark .custom-scrollbar-thumb.thumb-dragging {
  background: rgba(200, 200, 200, 0.7) !important;
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.15);
}

/* Smooth transitions for all elements */
.custom-scrollbar-container * {
  transition: all var(--scrollbar-animation-duration, 300ms) ease-in-out;
}
