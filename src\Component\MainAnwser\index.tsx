import React, { useState, useEffect, useRef, useLayoutEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { setInputValue, selectInputValue } from "../../slices/inputValueSlice";
import { setPluginInfo, selectPluginInfo } from "../../slices/pluginInfoSlice";
import {
  selectRightPanelExpanded,
  setRightPanelExpanded,
} from "../../slices/rightPanelSlice";
import {
  setQueryData as setReduxQueryData,
  clearSearchResults,
  setSelectedCategory,
  SerializedQueryResponse,
} from "../../slices/searchResultSlice";
import "../../index.css";
import {
  getResult,
  prepareSearch,
  searchAsync,
  removeResult,
  addCache,
  readAppConfig,
  getFrequentResult,
  searchAI,
  getUwpResult,
  runUwp,
  stopSearch,
} from "../../api/aiverything";
import {
  getPluginList,
  getPluginInfo,
  getPluginResourceUrl,
  pluginEnter,
} from "../../api/plugin";
import FileList from "../FileList";
import FileDetail from "../FileDetail";
import PluginList from "../PluginList";
import {
  openFileWithAdmin,
  openFileWithoutAdmin,
  showItemInFolder,
} from "../../api/system";
import PluginDetail from "../PluginDetail";
import PluginPage from "../PluginPage";
import { getCurrentWindow, LogicalSize } from "@tauri-apps/api/window";
import {
  isPermissionGranted,
  requestPermission,
  sendNotification,
} from "@tauri-apps/plugin-notification";
import { open } from "@tauri-apps/plugin-shell";
import { Footer } from "../Footer";
import { useTranslation } from "react-i18next";
import { MAIN_QUERY_WIDTH } from "../../App";
import { selectAIMode, selectEnterKeyState } from "../../slices/aiModeSlice";
import { CircularProgress } from "@mui/material";
import { WebviewWindow } from "@tauri-apps/api/webviewWindow";
import { QuickShortcuts } from "../QuickShortcuts";
import { FaGoogle } from "react-icons/fa";
import { BsBing } from "react-icons/bs";
import { SiDuckduckgo, SiBaidu } from "react-icons/si";

export enum DataType {
  SearchOnWeb = "Search",
  Shortcut = "Shortcut",
  App = "Apps",
  UwpApp = "UWP Apps",
  Folders = "Folders",
  Doc = "Documents",
  Sheet = "Sheets",
  Slide = "Slides",
  Picture = "Pictures",
  Video = "Videos",
  Audio = "Audios",
  Developer = "Developer",
  Others = "Others",
}
interface FileSize {
  number;
}

export interface Data {
  key: string;
  name: string;
  icon: string;
  type: DataType;
  extension: string;
  size: FileSize;
  path: string;
  lastModified?: Date;
  createdAt?: Date;
  metaData: any;
  appUserModelId?: string; // For UWP apps
  highlightPath?: string; // For highlighting matched keywords
  highlightFileName?: string; // For highlighting matched filename
}

interface UwpResult {
  displayName: string;
  name: string;
  highlightName?: string;
  version: string;
  architecture: number;
  resourceId: string;
  publisher: string;
  publisherId: string;
  fullName: string;
  familyName: string;
  installLocation: string;
  appUserModelId: string;
}

interface AiverthingCoreResponseData {
  path: string;
  modifyDate: string;
  fileSize: number;
  highlightPath?: string;
  highlightFileName?: string;
}

export type QueryResponse = {
  datatype: DataType;
  data: Data[];
};
export interface PluginInfo {
  author: string;
  className: string;
  description: string;
  detailedDescription: string;
  entryPage: string;
  icon: string;
  identifier: string;
  loadSuccess: boolean;
  name: string;
  version: string;
  embeddedSupport: boolean;
  embeddedPage: string;
}

export interface AiverthingCoreResponse {
  uuid: string;
  isDone: boolean;
  taskDuration?: number;
  data: [
    {
      dataType: string;
      results: AiverthingCoreResponseData[];
    }
  ];
}

const transformCoreDataToData = (
  coreData: AiverthingCoreResponseData,
  type: DataType
): Data => {
  const fileName = coreData.path.substring(coreData.path.lastIndexOf("\\") + 1);
  const extensionIndex = fileName.lastIndexOf(".");
  const extension =
    extensionIndex !== -1 ? fileName?.substring(extensionIndex + 1) : "";

  return {
    key: coreData.path,
    name: fileName,
    icon: "",
    type: type,
    extension: extension,
    size: { number: coreData.fileSize },
    path: coreData.path,
    lastModified: new Date(coreData.modifyDate),
    createdAt: new Date(), // Assuming createdAt is not available in coreData
    metaData: {}, // Assuming metaData is not available in coreData
    highlightPath: coreData.highlightPath, // Add highlightPath field
    highlightFileName: coreData.highlightFileName, // Add highlightFileName field
  };
};

const transformUwpResultToData = (uwpResult: UwpResult): Data => {
  return {
    key: uwpResult.fullName,
    name: uwpResult.displayName,
    icon: "",
    type: DataType.UwpApp,
    extension: "",
    size: { number: 0 },
    path: uwpResult.installLocation,
    lastModified: undefined,
    createdAt: undefined,
    metaData: uwpResult,
    appUserModelId: uwpResult.appUserModelId,
    highlightFileName: uwpResult.highlightName, // Add highlightName field for UWP apps
  };
};

const transformCoreResponseToQueryResponse = (
  coreResponse: AiverthingCoreResponse
): QueryResponse[] => {
  const dataTypes: { [key: string]: DataType } = {
    Shortcut: DataType.Shortcut,
    App: DataType.App,
    Folder: DataType.Folders,
    Doc: DataType.Doc,
    Sheet: DataType.Sheet,
    Slide: DataType.Slide,
    Picture: DataType.Picture,
    Audio: DataType.Audio,
    Video: DataType.Video,
    Developer: DataType.Developer,
    Default: DataType.Others,
  };

  const queryResponses: QueryResponse[] = [];

  coreResponse?.data?.forEach((eachDataTypeResult) => {
    const coreDataArray = eachDataTypeResult.results;
    const dataType = dataTypes[eachDataTypeResult.dataType] || DataType.Others;
    if (coreDataArray?.length > 0) {
      const dataArray = coreDataArray.map((eachCoreData) =>
        transformCoreDataToData(eachCoreData, dataType)
      );
      let existDataTypeArr = queryResponses.filter((eachQueryResp) => {
        return eachQueryResp.datatype === dataType;
      });
      if (existDataTypeArr?.length > 0) {
        existDataTypeArr[0].data.push(...dataArray);
      } else {
        queryResponses.push({
          datatype: dataType,
          data: dataArray,
        });
      }
    }
  });

  return queryResponses;
};

// 清理非序列化数据的函数
const cleanQueryDataForRedux = (
  queryData: QueryResponse[]
): SerializedQueryResponse[] => {
  return queryData.map((section) => ({
    datatype: section.datatype as string,
    data: section.data.map((item) => ({
      ...item,
      type: item.type as string, // 转换DataType enum为string
      lastModified: item.lastModified
        ? item.lastModified.toISOString()
        : undefined,
      createdAt: item.createdAt ? item.createdAt.toISOString() : undefined,
      metaData: {
        ...item.metaData,
        searchEngineIcon: undefined, // 移除React元素
      },
    })),
  })) as SerializedQueryResponse[];
};

export const MainAnswer: React.FC = () => {
  const dispatch = useDispatch();
  const inputValue = useSelector(selectInputValue);
  const currentPluginInfo = useSelector(selectPluginInfo);
  const [selectedFile, setSelectedFile] = useState<Data>();
  const [selectedPlugin, setSelectedPlugin] = useState<PluginInfo>();
  const [searchState, setSearchState] = useState<
    "idle" | "waiting" | "loading"
  >("idle");
  const [queryData, setQueryData] = useState<QueryResponse[]>([]);
  const [pluginInfoList, setPluginInfoList] = useState<PluginInfo[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const prepareTimeRef = useRef<NodeJS.Timeout | null>(null);
  const lastSearchUuidRef = useRef<string>(null);
  const inputValueRef = useRef<string>(inputValue);
  const [pluginPrefix, setPluginPrefix] = useState<string>(null);
  const [appConfig, setAppConfig] = useState<any>(null);
  const aiMode = useSelector(selectAIMode);
  const aiModeRef = useRef(aiMode);
  const enterKeyState = useSelector(selectEnterKeyState);
  const [aiSearchState, setAiSearchState] = useState(0);
  const [showEmbeddedPage, setShowEmbeddedPage] = useState(false);
  const [windowState, setWindowState] = useState("shrink");
  const isRightPanelExpanded = useSelector(selectRightPanelExpanded);
  const [taskDuration, setTaskDuration] = useState<number | null>(null);
  const [isCtrlPressed, setIsCtrlPressed] = useState<boolean>(false);

  const { t } = useTranslation();

  const maxRetryTimes = 100;
  const maxResultNum = 5000;
  const maxDisplayResults = 200; // 显示结果的最大数量

  // 限制查询数据大小的函数
  const limitQueryData = (data: QueryResponse[]): QueryResponse[] => {
    if (!data || data.length === 0) return [];

    const limitedData: QueryResponse[] = [];
    let totalCount = 0;

    for (const section of data) {
      if (!section || !section.data) continue;

      const availableSlots = maxDisplayResults - totalCount;
      if (availableSlots <= 0) break;

      const limitedSection = {
        ...section,
        data: section.data.slice(0, availableSlots),
      };

      limitedData.push(limitedSection);
      totalCount += limitedSection.data.length;

      if (totalCount >= maxDisplayResults) break;
    }

    return limitedData;
  };

  // 计算总结果数量
  const totalResultCount = queryData.reduce((total, queryResponse) => {
    return total + queryResponse.data.length;
  }, 0);

  const sendSystemNotification = async (title, body) => {
    // Do you have permission to send a notification?
    let permissionGranted = await isPermissionGranted();

    // If not we need to request it
    if (!permissionGranted) {
      const permission = await requestPermission();
      permissionGranted = permission === "granted";
    }
    // Once permission has been granted we can send the notification
    if (permissionGranted) {
      sendNotification({ title, body });
    }
  };

  useEffect(() => {
    readAppConfig()
      .then((res) => {
        setAppConfig(res);
      })
      .catch((err) => {
        console.error(err);
      });
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Alt") {
        event.preventDefault();
      }

      // 检测 Ctrl 键
      if (event.key === "Control") {
        setIsCtrlPressed(true);
        return;
      }

      if (event.key === "Enter" && inputValue) {
        // 进入插件模式后，inputValue不会更新，所以inputValue.startsWith(">")始终为true
        if (inputValue.startsWith(">")) {
          // 当currentPluginInfo不为null，则已进入插件模式，这里不进行任何操作，由PluginPage转发事件到插件
          if (!currentPluginInfo) {
            if (selectedPlugin) {
              // 切换到插件模式
              onPluginClick(selectedPlugin);
            }
          }
        } else if (selectedFile && !currentPluginInfo && queryData.length > 0) {
          const lastUuid = lastSearchUuidRef.current;
          if (lastUuid) {
            stopSearch(lastUuid);
          }
          console.log("Selected file: ", selectedFile);
          const urlTemplate = appConfig?.default_search_engine
            ?.url_template as string;
          switch (selectedFile.type) {
            case DataType.SearchOnWeb:
              console.log("Search on web: ", inputValue);
              if (urlTemplate) {
                open(urlTemplate.replace("%s", inputValue));
              }
              break;
            case DataType.UwpApp:
              if (selectedFile.appUserModelId) {
                runUwp(selectedFile.appUserModelId).catch((error) => {
                  console.error("Failed to launch UWP app:", error);
                });
              }
              break;
            default:
              addCache(selectedFile.path);
              openFileWithoutAdmin(selectedFile.path);
              break;
          }
          getCurrentWindow().hide();
        }
      } else if (event.ctrlKey && event.key.toLowerCase() === "c") {
        // Ctrl + C: 复制文件路径
        addCache(selectedFile.path);
        navigator.clipboard.writeText(selectedFile.path);
        sendSystemNotification("Aiverything", t("searchBar.filePathCopied"));
        getCurrentWindow().hide();
      } else if (event.ctrlKey && event.key.toLowerCase() === "p") {
        // Ctrl + P: 打开上级文件夹
        addCache(selectedFile.path);
        showItemInFolder(selectedFile.path);
        getCurrentWindow().hide();
      } else if (event.ctrlKey && event.key.toLowerCase() === "o") {
        // Ctrl + O: 以管理员权限打开
        if (selectedFile?.type !== DataType.UwpApp) {
          addCache(selectedFile.path);
          openFileWithAdmin(selectedFile.path);
          getCurrentWindow().hide();
        } else {
          sendSystemNotification(
            "Aiverything",
            t("searchBar.uwpAppNotSupport")
          );
        }
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [
    selectedFile,
    selectedPlugin,
    currentPluginInfo,
    queryData,
    isCtrlPressed,
  ]);

  useEffect(() => {
    readAppConfig()
      .then((res) => {
        setAppConfig(res);
      })
      .catch((err) => {
        console.error(err);
      });
    const handleKeyUp = (event: KeyboardEvent) => {
      // 检测 Ctrl 键释放
      if (event.key === "Control") {
        setIsCtrlPressed(false);
      }
    };
    window.addEventListener("keyup", handleKeyUp);
    return () => {
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, [selectedFile, selectedPlugin, currentPluginInfo, isCtrlPressed]);

  // 同步selectedFile与selectedCategory
  useEffect(() => {
    if (selectedFile?.type) {
      // 如果选中的文件类型不是 "Search"，则更新分类
      if (selectedFile.type !== DataType.SearchOnWeb) {
        dispatch(setSelectedCategory(selectedFile.type));
      }
    }
  }, [selectedFile, dispatch]);

  const searchAIAndShowResult = (searchText: string, maxResultNum: number) => {
    setSearchState("loading");
    setAiSearchState(1);
    setTaskDuration(null);
    searchAI(searchText, maxResultNum)
      .then((res) => res.data)
      .then((res) => {
        const newData = transformCoreResponseToQueryResponse(res);
        const limitedData = limitQueryData(newData);
        console.log("AI search result", limitedData);
        setQueryData(limitedData);
        dispatch(setReduxQueryData(cleanQueryDataForRedux(limitedData as any)));
        setSearchState("idle");
        setAiSearchState(2);
      })
      .catch((error) => {
        console.error("AI search failed:", error);
        setSearchState("idle");
        setAiSearchState(2);
      });
  };

  const fetchResultsUntilDone = async (
    currentInput: string,
    uuid: string,
    retryTimes: number
  ) => {
    if (
      lastSearchUuidRef.current !== uuid ||
      currentInput !== inputValueRef.current
    ) {
      return;
    }
    if (aiModeRef.current) {
      console.log("Stopping due to switching to AI mode");
      setSearchState("idle");
      return;
    }
    const res = await getResult(uuid);
    console.log("True Search with uuid: ", res);

    // 提取taskDuration
    const duration = res?.data?.taskDuration;
    if (duration !== undefined && duration !== null && duration !== 0) {
      setTaskDuration(duration);
    }

    if (
      lastSearchUuidRef.current !== uuid ||
      currentInput !== inputValueRef.current
    ) {
      return;
    }
    const newData = transformCoreResponseToQueryResponse(res.data);
    console.log("New data: ", newData);

    const uwpResultList = await getUwpResult(uuid);
    const uwpRes: UwpResult[] = uwpResultList.data;
    console.log("UWP result: ", uwpRes);

    // Transform UWP results to Data and add to newData
    if (uwpRes && uwpRes.length > 0) {
      const uwpData = uwpRes.map(transformUwpResultToData);
      newData.unshift({
        datatype: DataType.UwpApp,
        data: uwpData,
      });
    }

    let searchEngineIcon: any;
    let searchEngineName: string;
    switch (appConfig?.default_search_engine?.name) {
      case "Baidu":
        searchEngineIcon = <SiBaidu className="w-full h-full object-contain" />;
        searchEngineName = "Baidu";
        break;
      case "Bing":
        searchEngineIcon = <BsBing className="w-full h-full object-contain" />;
        searchEngineName = "Bing";
        break;
      case "DuckDuckGo":
        searchEngineIcon = (
          <SiDuckduckgo className="w-full h-full object-contain" />
        );
        searchEngineName = "DuckDuckGo";
        break;
      default:
        searchEngineIcon = (
          <FaGoogle className="w-full h-full object-contain" />
        );
        searchEngineName = "Google";
        break;
    }
    const searchOnWeb: Data = {
      key: "web",
      name: t("searchBar.detail.searchFor", { inputValue }),
      icon: "", // 存储空字符串，实际图标通过metaData.searchEngine获取
      path: "",
      type: DataType.SearchOnWeb,
      extension: "",
      size: { number: 0 },
      lastModified: undefined,
      createdAt: undefined,
      metaData: {
        searchEngine: searchEngineName,
        searchEngineIcon: searchEngineIcon, // 临时存储，不会被序列化到Redux
      },
    };
    newData.unshift({
      datatype: DataType.SearchOnWeb,
      data: [searchOnWeb],
    });

    // 应用数据限制
    const limitedData = limitQueryData(newData);

    if (limitedData.length > 1) {
      setSearchState("idle");
    }
    setQueryData(limitedData);
    dispatch(setReduxQueryData(cleanQueryDataForRedux(limitedData as any)));
    if (res.data.isDone || retryTimes === 0) {
      console.log("True Search happened: ", res);
      setSearchState("idle");
    } else {
      if (
        lastSearchUuidRef.current !== uuid ||
        currentInput !== inputValueRef.current
      ) {
        return;
      }
      setTimeout(() => {
        fetchResultsUntilDone(currentInput, uuid, retryTimes - 1);
      }, 500);
    }
  };

  const isValidUrl = (urlString: string) => {
    try {
      return Boolean(new URL(urlString));
    } catch (e) {
      return false;
    }
  };

  const fetchPluginInfoList = async (inputPluginName: string) => {
    try {
      const pluginListData = await getPluginList(inputPluginName);
      const pluginList = pluginListData.data;
      const pluginInfoArray = pluginList.map(async (eachPlugin: any) => {
        const eachPluginInfo = await getPluginInfo(
          eachPlugin.identifier,
          appConfig.locale
        );
        const pluginInfo: PluginInfo = eachPluginInfo.data;
        const eachPluginIconUrl = await getPluginResourceUrl(
          eachPlugin.identifier,
          pluginInfo.icon
        );
        const eachPluginEntryPageUrl = await getPluginResourceUrl(
          eachPlugin.identifier,
          pluginInfo.entryPage
        );
        if (pluginInfo.embeddedSupport) {
          let eachPluginEmbeddedPage = pluginInfo.embeddedPage
            ? pluginInfo.embeddedPage
            : await getPluginResourceUrl(
                eachPlugin.identifier,
                "embedded.html"
              );
          if (!isValidUrl(eachPluginEmbeddedPage)) {
            eachPluginEmbeddedPage = await getPluginResourceUrl(
              eachPlugin.identifier,
              eachPluginEmbeddedPage
            );
          }
          pluginInfo.embeddedPage = eachPluginEmbeddedPage;
        }

        pluginInfo.icon = eachPluginIconUrl;
        pluginInfo.entryPage = eachPluginEntryPageUrl;
        return pluginInfo;
      });
      const pluginInfos = await Promise.all(pluginInfoArray);
      console.log("Plugin Info List: ", pluginInfos);
      setPluginInfoList(pluginInfos);
    } catch (err) {
      console.error("Fetch plugin info list failed: ", err);
    }
  };

  useLayoutEffect(() => {
    // 更新inputValue的ref
    inputValueRef.current = inputValue;

    if (inputValue !== null && inputValue !== undefined && inputValue !== "") {
      //重置按键
      dispatch(setRightPanelExpanded(false));

      // 如果是 AI 模式，需要等待回车键
      if (aiMode) {
        // 只有在按下回车键时才触发搜索
        if (enterKeyState && aiSearchState !== 1) {
          searchAIAndShowResult(inputValue, maxResultNum);
        }
        return;
      }

      // 立即设置waiting状态，避免窗口闪烁
      if (pluginPrefix === null && !inputValue.startsWith(">")) {
        setSearchState("waiting");
        setTaskDuration(null);
      }

      if (pluginPrefix === null) {
        if (inputValue.startsWith(">")) {
          // 获取插件列表
          const inputPluginName = inputValue.substring(1);
          if (inputPluginName.endsWith(" ")) {
            // 去掉末尾的空格
            const fullInputPluginName = inputPluginName.trimEnd();
            const filteredPlugin = pluginInfoList.filter(
              (eachPlugin) => eachPlugin.identifier === fullInputPluginName
            );
            if (filteredPlugin.length > 0) {
              // 进入插件模式
              const currentPluginInfo = filteredPlugin[0];
              onPluginClick(currentPluginInfo);
            }
          } else {
            setPluginPrefix(null);
            dispatch(setPluginInfo(null));
            fetchPluginInfoList(inputPluginName);
          }
        } else if (inputValue.startsWith(":")) {
          const userInput = inputValue.substring(1);
          // 清除之前可能存在的搜索结果缓存
          const lastUuid = lastSearchUuidRef.current;
          if (lastUuid) {
            removeResult(lastUuid);
            stopSearch(lastUuid);
            lastSearchUuidRef.current = null;
          }
          setTaskDuration(null);
          getFrequentResult(-1, userInput)
            .then((res) => {
              const newData = transformCoreResponseToQueryResponse(res);
              const limitedData = limitQueryData(newData);
              setQueryData(limitedData);
              dispatch(
                setReduxQueryData(cleanQueryDataForRedux(limitedData as any))
              );
              setSearchState("idle");
            })
            .catch((err) => {
              console.error("Fetch frequent result failed: ", err);
              setSearchState("idle");
            });
        } else {
          setPluginPrefix(null);
          dispatch(setPluginInfo(null));
          // 文件搜索模式
          // 清除之前的计时器
          if (timerRef.current) {
            clearTimeout(timerRef.current);
            console.log("Clear timer");
          }

          if (prepareTimeRef.current) {
            clearTimeout(prepareTimeRef.current);
            console.log("Clear prepare timer");
          }
          let lastUuid = lastSearchUuidRef.current;
          if (!lastUuid) {
            lastUuid = "";
          }
          stopSearch(lastUuid).then(() => {
            prepareTimeRef.current = setTimeout(() => {
              (async () => {
                console.log("Prepare Search with", inputValue);
                setTaskDuration(null);
                const lastUuidToRemove = lastSearchUuidRef.current;
                if (lastUuidToRemove !== null) {
                  await removeResult(lastUuidToRemove);
                }
                const prepareRes = await prepareSearch(
                  inputValue,
                  maxResultNum
                );
                const res = prepareRes?.data;
                console.log("Search prepared: ", res);
                lastSearchUuidRef.current = res;
                setSelectedFile(null);
                setSearchState("loading");
                await fetchResultsUntilDone(inputValue, res, 0);

                if (timerRef.current) {
                  clearTimeout(timerRef.current);
                }

                // 设置新的计时器
                timerRef.current = setTimeout(() => {
                  (async () => {
                    // 在400ms内inputValue没有变化时，执行一些操作
                    console.log("True Search with", inputValue);
                    const searchRes = await searchAsync(
                      inputValue,
                      maxResultNum
                    );
                    const res = searchRes?.data;
                    console.log("Start search: ", res);
                    if (lastSearchUuidRef.current !== res) {
                      lastSearchUuidRef.current = res;
                      setSelectedFile(null);
                    }
                    await fetchResultsUntilDone(inputValue, res, maxRetryTimes);
                  })().catch((err) => {
                    console.error("True search failed: ", err);
                  });
                }, 400);
              })().catch((err) => {
                console.error("Prepare search failed: ", err);
              });
            }, 200);
          });
        }
      }
    }
    // 清除计时器
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      if (prepareTimeRef.current) {
        clearTimeout(prepareTimeRef.current);
      }
    };
  }, [inputValue, aiMode, enterKeyState, pluginPrefix]);

  // 添加处理输入值为空时的逻辑
  useEffect(() => {
    if (!inputValue) {
      // 清空搜索结果
      setQueryData([]);
      dispatch(clearSearchResults());
      setSelectedFile(null);
      setSearchState("idle");
      setTaskDuration(null);

      // 清除搜索缓存
      const lastUuid = lastSearchUuidRef.current;
      if (lastUuid) {
        removeResult(lastUuid);
        stopSearch(lastUuid);
        lastSearchUuidRef.current = null;
      }

      // 清除计时器
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
      if (prepareTimeRef.current) {
        clearTimeout(prepareTimeRef.current);
        prepareTimeRef.current = null;
      }
    }
  }, [inputValue]);

  useEffect(() => {
    if (currentPluginInfo === null) {
      setPluginPrefix(null);
    }
  }, [currentPluginInfo]);

  // 监听Tauri窗口事件
  useEffect(() => {
    let unlisten: (() => void) | null = null;

    const setupWindowListener = async () => {
      const currentWindow = getCurrentWindow();

      // 监听窗口失去焦点事件
      unlisten = await currentWindow.onFocusChanged(({ payload: focused }) => {
        if (!focused) {
          console.log("Window lost focus, stop searching...");
          const lastUuid = lastSearchUuidRef.current;
          if (lastUuid) {
            stopSearch(lastUuid);
            lastSearchUuidRef.current = null;
          }
        }
      });
    };

    setupWindowListener().catch((err) => {
      console.error("设置窗口监听器失败:", err);
    });

    return () => {
      if (unlisten) {
        unlisten();
      }
    };
  }, []);

  const onPluginClick = async (plugin: PluginInfo) => {
    if (plugin?.embeddedSupport) {
      try {
        // 调用 pluginEnter
        const shouldShowDirectly = await pluginEnter(plugin.identifier);
        console.log("Should show directly: ", shouldShowDirectly?.data);

        // 根据返回值决定是否直接显示嵌入页面
        setShowEmbeddedPage(shouldShowDirectly?.data);
        // 进入插件模式，清空input并在input前添加一个插件图标
        dispatch(setPluginInfo(plugin));
        dispatch(setInputValue(""));
        setPluginPrefix(plugin.identifier);

        console.log("Enter plugin mode: ", plugin);
      } catch (error) {
        console.error("Plugin enter failed:", error);
      }
    } else {
      // 打开插件页面
      const entryPageTitle = `entryPage-${plugin.identifier}`;
      WebviewWindow.getByLabel(entryPageTitle)
        .then((window) => {
          window.unminimize();
          window.setFocus();
        })
        .catch(() => {
          WebviewWindow.getByLabel("main").then((window) => {
            window.hide();
          });
          const webview = new WebviewWindow(entryPageTitle, {
            url: plugin.entryPage,
            width: 800,
            height: 600,
            center: true,
            title: plugin.name,
          });

          webview.once("tauri://created", function () {
            // webview window successfully created
          });
          webview.once("tauri://error", function (e) {
            console.error("Error creating entry page window:", e);
          });
        });
    }
  };

  const expandWindowHeight = async () => {
    const appWindow = getCurrentWindow();
    await appWindow.setResizable(true);

    const scaleFactor = await appWindow.scaleFactor();
    const ratio = window.devicePixelRatio / scaleFactor;

    const newSize = new LogicalSize(MAIN_QUERY_WIDTH * ratio, 564 * ratio);
    await appWindow.setSize(newSize);
    await appWindow.setResizable(false);
    console.log("Expand window");
    setWindowState("expand");
  };

  const shrinkWindowHeight = async () => {
    // 增加高度以确保显示快捷栏和Footer
    // MainInput h-14=3.5rem  Footer h-6=1.5rem  QuickShortcuts 10rem
    const INPUT_HEIGHT_REM = 3.5;
    const QUICK_SHORTCUTS_HEIGHT_REM = 10;
    const FOOTER_HEIGHT_REM = 1.5;
    const TOTAL_HEIGHT_REM =
      INPUT_HEIGHT_REM + QUICK_SHORTCUTS_HEIGHT_REM + FOOTER_HEIGHT_REM;
    const REM_TO_PX = 16; // 1rem = 16px
    const windowHeight = Math.ceil(TOTAL_HEIGHT_REM * REM_TO_PX);
    const currentWindow = getCurrentWindow();
    await currentWindow.setResizable(true);

    const scaleFactor = await currentWindow.scaleFactor();
    const ratio = window.devicePixelRatio / scaleFactor;

    await currentWindow.setSize(
      new LogicalSize(MAIN_QUERY_WIDTH * ratio, windowHeight * ratio)
    );
    await currentWindow.setResizable(false);
    console.log("Shrink window");
    setWindowState("shrink");

    console.log("resizeToEmpty, height:", windowHeight);
  };

  useEffect(() => {
    if (
      (!inputValue && !showEmbeddedPage) ||
      (!inputValue && currentPluginInfo === null)
    ) {
      shrinkWindowHeight();
    }
  }, [inputValue, showEmbeddedPage, currentPluginInfo]);

  useEffect(() => {
    aiModeRef.current = aiMode;
    setAiSearchState(0);
    setQueryData([]);
    dispatch(clearSearchResults());
    setSelectedFile(null);
    setTaskDuration(null);
    setSearchState("idle");
    shrinkWindowHeight();
  }, [aiMode]);

  // 添加一个 useEffect 来处理窗口大小
  useEffect(() => {
    if (showEmbeddedPage && currentPluginInfo) {
      expandWindowHeight();
    } else if (
      inputValue &&
      (!aiMode || aiSearchState !== 0) &&
      searchState !== "loading" &&
      searchState !== "waiting" &&
      (queryData.length > 0 ||
        currentPluginInfo !== null ||
        inputValue?.startsWith(">"))
    ) {
      expandWindowHeight();
    } else if (
      inputValue &&
      (searchState === "loading" || searchState === "waiting")
    ) {
      shrinkWindowHeight();
    }
  }, [
    inputValue,
    aiMode,
    aiSearchState,
    showEmbeddedPage,
    currentPluginInfo,
    searchState,
    queryData,
    pluginInfoList,
  ]);

  // 修改渲染逻辑
  let element = null;
  if (inputValue || (currentPluginInfo && showEmbeddedPage)) {
    if (aiMode && aiSearchState === 0) {
      // AI 模式下，只有在未开始搜索时显示按回车搜索的提示
      element = (
        <div
          className="flex bg-main-query items-center justify-center w-full transition-opacity dark:bg-gray-800"
          style={{
            background: "transparent",
            height: "10rem", // 固定为收起状态的高度
          }}
        >
          <div className="text-center">
            <div className="flex flex-col items-center gap-4">
              <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-6 w-6 text-blue-500 dark:text-blue-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
              </div>
              <div className="text-lg text-gray-700 dark:text-gray-300 font-medium">
                {t("searchBar.pressEnterToSearch")}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {t("searchBar.aiModeHint")}
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      if (searchState === "loading") {
        element = (
          <div
            className="flex bg-main-query items-center justify-center w-full transition-opacity dark:bg-gray-800"
            style={{
              background: "transparent",
              height: "10rem", // 固定为收起状态的高度
            }}
          >
            <div className="text-center">
              <div className="flex flex-col items-center gap-4">
                <CircularProgress size={40} className="text-blue-500" />
                <div className="animate-pulse text-lg text-gray-600 dark:text-gray-300">
                  {aiMode
                    ? t("searchBar.aiSearching")
                    : t("searchBar.searching")}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {aiMode
                    ? t("searchBar.aiSearchPrompt")
                    : t("searchBar.searchPrompt")}
                </div>
              </div>
            </div>
          </div>
        );
      } else if (searchState === "waiting") {
        element = (
          <div
            className="flex bg-main-query items-center justify-center w-full transition-opacity dark:bg-gray-800"
            style={{
              background: "transparent",
              height: "10rem", // 固定为收起状态的高度
            }}
          >
            <div className="text-center">
              <div className="flex flex-col items-center gap-4">
                <div className="w-12 h-12 rounded-full bg-gray-100/50 dark:bg-gray-800/50 backdrop-blur-sm flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-8 w-8 text-gray-400 dark:text-gray-500"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <defs>
                      <linearGradient
                        id="gradient"
                        x1="0%"
                        y1="0%"
                        x2="100%"
                        y2="100%"
                      >
                        <stop
                          offset="0%"
                          stopColor="currentColor"
                          stopOpacity="0.8"
                        />
                        <stop
                          offset="50%"
                          stopColor="currentColor"
                          stopOpacity="0.4"
                        />
                        <stop
                          offset="100%"
                          stopColor="currentColor"
                          stopOpacity="0.1"
                        />
                      </linearGradient>
                    </defs>
                    <circle
                      cx="12"
                      cy="12"
                      r="8"
                      stroke="url(#gradient)"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeDasharray="25 8"
                      fill="none"
                      className="animate-spin"
                    />
                    <circle
                      cx="12"
                      cy="12"
                      r="6"
                      stroke="currentColor"
                      strokeWidth="1"
                      strokeOpacity="0.2"
                      fill="none"
                    />
                  </svg>
                </div>
                <div className="animate-pulse text-lg text-gray-600 dark:text-gray-300">
                  {t("searchBar.waitingForInput")}
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {t("searchBar.waitingPrompt")}
                </div>
              </div>
            </div>
          </div>
        );
      } else if (inputValue?.startsWith(">") && pluginPrefix === null) {
        element = (
          <div
            className="flex bg-main-query dark:bg-gray-800 relative h-answer"
            style={{
              background: "transparent",
            }}
          >
            <div className="flex-1 overflow-y-auto">
              <PluginList
                data={pluginInfoList}
                onSelect={setSelectedPlugin}
                onItemClick={onPluginClick}
              />
            </div>
            <div
              className={`transition-all duration-300 overflow-y-auto ${
                isRightPanelExpanded ? "w-1/2 visible" : "w-0 invisible"
              }`}
            >
              <div className="h-full">
                <PluginDetail
                  plugin={selectedPlugin}
                  searchKeyword={
                    inputValue?.startsWith(">") ? inputValue.substring(1) : ""
                  }
                />
              </div>
            </div>
          </div>
        );
      } else if (pluginPrefix !== null) {
        // 获取插件页面
        element = (
          <div
            className="flex bg-main-query dark:bg-gray-800 relative h-answer"
            style={{
              background: "transparent",
            }}
          >
            <div className="flex-1 overflow-y-auto">
              <PluginPage
                url={selectedPlugin?.embeddedPage}
                inputValue={inputValue}
              />
            </div>
          </div>
        );
      } else {
        element = (
          <div
            className="flex bg-main-query dark:bg-gray-800 relative h-answer"
            style={{
              background: "transparent",
            }}
          >
            <div className="flex-1 overflow-y-auto">
              <FileList
                data={queryData}
                onSelect={setSelectedFile}
                isRightPanelExpanded={isRightPanelExpanded}
                searchUuid={lastSearchUuidRef.current}
              />
            </div>
            <div
              className={`transition-all duration-300 overflow-y-auto ${
                isRightPanelExpanded ? "w-1/2 visible" : "w-0 invisible"
              }`}
            >
              <div className="h-full">
                <FileDetail file={selectedFile} />
              </div>
            </div>
          </div>
        );
      }
    }
  }

  // 当输入为空时，显示快捷栏和Footer
  if (!inputValue && !currentPluginInfo) {
    return (
      <React.Fragment>
        <div
          className="transition-all duration-300 ease-in-out bg-transparent"
          style={{
            maxHeight: "10rem",
            overflow: "hidden",
            marginBottom: "0", // 确保与Footer无间距
            borderRadius: "0", // 移除可能的圆角，避免与Footer产生冲突
          }}
        >
          <QuickShortcuts />
        </div>
        <Footer
          isCtrlPressed={isCtrlPressed}
          inputValue={inputValue}
          loading={searchState === "loading"}
          taskDuration={taskDuration}
          totalResultCount={totalResultCount}
          selectedFile={selectedFile}
        />
      </React.Fragment>
    );
  }

  return (
    <React.Fragment>
      <div
        className={`transition-all duration-500 ease-in-out ${
          windowState === "expand" ? "max-h-108" : "max-h-40"
        }`}
        style={{
          background: "transparent",
        }}
      >
        {element}
      </div>
      <Footer
        isCtrlPressed={isCtrlPressed}
        inputValue={inputValue}
        loading={searchState === "loading"}
        taskDuration={taskDuration}
        totalResultCount={totalResultCount}
        selectedFile={selectedFile}
      />
    </React.Fragment>
  );
};
