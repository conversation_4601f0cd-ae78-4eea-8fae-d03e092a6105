use std::os::windows::process::CommandExt;

use crate::{api::lib::AiverythingApi, utils::bat_vbs};
use serde_json::Value;
use window_vibrancy::{apply_acrylic, clear_acrylic};

pub const CORE_PORT: &str = "50721";
const CORE_URL: &str = "https://localhost:";

lazy_static::lazy_static! {
    static ref aiverything_api: AiverythingApi = AiverythingApi::new((CORE_URL.to_string() + CORE_PORT).as_str())
    .expect("Failed to create API instance");
    static ref vibrancy_state: std::sync::Mutex<bool> = std::sync::Mutex::new(false);
    static ref main_window: std::sync::Mutex<Option<tauri::WebviewWindow>> =
        std::sync::Mutex::new(None);
}

#[tauri::command]
pub async fn status() -> Result<Value, String> {
    match aiverything_api.status().await {
        Ok(status) => Ok(status),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_config() -> Result<Value, String> {
    match aiverything_api.get_config().await {
        Ok(config) => Ok(config),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_gpu() -> Result<Value, String> {
    match aiverything_api.get_gpu().await {
        Ok(status) => Ok(status),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_disk() -> Result<Value, String> {
    match aiverything_api.get_disk().await {
        Ok(status) => Ok(status),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn set_config(config: serde_json::Value) -> Result<Value, String> {
    match aiverything_api.set_config(&config).await {
        Ok(status) => Ok(status),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn close() -> Result<Value, String> {
    match aiverything_api.close().await {
        Ok(status) => Ok(status),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn prepare_search(search_text: String, max_result_num: i32) -> Result<Value, String> {
    match aiverything_api
        .prepare_search(&search_text, max_result_num)
        .await
    {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn search_async(search_text: String, max_result_num: i32) -> Result<Value, String> {
    match aiverything_api
        .search_async(&search_text, max_result_num)
        .await
    {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn remove_summary(session_id: String) -> Result<Value, String> {
    match aiverything_api.remove_summary(&session_id).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_summary(session_id: String) -> Result<Value, String> {
    match aiverything_api.get_summary(&session_id).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn summarize_ai(file: String) -> Result<Value, String> {
    match aiverything_api.summarize_ai(&file).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn search_ai(search_text: String, max_result_num: i32) -> Result<Value, String> {
    match aiverything_api
        .search_ai(&search_text, max_result_num)
        .await
    {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn stop_search(uuid: String) -> Result<Value, String> {
    match aiverything_api.stop_search(&uuid).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_result(uuid: String) -> Result<Value, String> {
    match aiverything_api.get_result(&uuid).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_uwp_result(uuid: String) -> Result<Value, String> {
    match aiverything_api.get_uwp_result(&uuid).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn remove_result(uuid: String) -> Result<Value, String> {
    match aiverything_api.remove_result(&uuid).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn version() -> Result<Value, String> {
    match aiverything_api.version().await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn build_version() -> Result<Value, String> {
    match aiverything_api.build_version().await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn flush_file_changes() -> Result<Value, String> {
    match aiverything_api.flush_file_changes().await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn add_cache(path: String) -> Result<Value, String> {
    match aiverything_api.add_cache(&path).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_cache() -> Result<Value, String> {
    match aiverything_api.get_cache().await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn delete_cache(path: String) -> Result<Value, String> {
    match aiverything_api.delete_cache(&path).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_frequent_result(num: i32, search_text: String) -> Result<Value, String> {
    match aiverything_api.get_frequent_result(num, search_text).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_icon(path: String, is_uwp: bool) -> Result<Value, String> {
    match aiverything_api.get_icon(&path, is_uwp).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn update(is_drop_previous: bool) -> Result<Value, String> {
    match aiverything_api.update(is_drop_previous).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn run_uwp(app_user_model_id: String) -> Result<Value, String> {
    match aiverything_api.run_uwp(&app_user_model_id).await {
        Ok(result) => Ok(result),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn start_new_program_without_admin(path: String) -> Result<Value, String> {
    let ret = Value::Null;
    match start_new_program_using_explorer(&path) {
        Ok(_) => Ok(ret),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn start_new_program_with_admin(path: String) -> Result<Value, String> {
    let ret = Value::Null;
    match start_new_program_using_vbs(&path) {
        Ok(_) => Ok(ret),
        Err(e) => Err(e),
    }
}

#[tauri::command]
pub fn get_window_acrylic_state() -> bool {
    if let Ok(vibrancy_state_guard) = vibrancy_state.lock() {
        vibrancy_state_guard.clone()
    } else {
        false
    }
}

#[tauri::command]
pub fn apply_window_acrylic() {
    let window = main_window.lock();
    if let Ok(window) = window.as_ref() {
        set_window_acrylic_impl(window.as_ref().unwrap());
    }
}

#[tauri::command]
pub fn clear_window_acrylic() {
    let window = main_window.lock();
    if let Ok(window) = window.as_ref() {
        clear_window_acrylic_impl(window.as_ref().unwrap());
    }
}

pub fn set_main_window(window: tauri::WebviewWindow) {
    if let Ok(mut main_window_mut) = main_window.lock() {
        *main_window_mut = Some(window);
    } else {
        eprintln!("Failed to lock main_window");
    }
}

pub fn clear_window_acrylic_impl(window: &tauri::WebviewWindow) {
    #[cfg(target_os = "windows")]
    clear_acrylic(&window)
        .expect("Unsupported platform! 'clear_acrylic' is only supported on Windows");
    if let Ok(mut vibrancy_state_mut) = vibrancy_state.lock() {
        *vibrancy_state_mut = false;
    } else {
        eprintln!("Failed to lock vibrancy_state");
    }
}

pub fn set_window_acrylic_impl(window: &tauri::WebviewWindow) {
    #[cfg(target_os = "windows")]
    apply_acrylic(&window, Some((243, 243, 243, 125)))
        .expect("Unsupported platform! 'apply_acrylic' is only supported on Windows");
    if let Ok(mut vibrancy_state_mut) = vibrancy_state.lock() {
        *vibrancy_state_mut = true;
    } else {
        eprintln!("Failed to lock vibrancy_state");
    }
}

fn start_new_program_using_explorer(path: &str) -> Result<(), Box<dyn std::error::Error>> {
    let program_path = std::path::Path::new(path);
    if !program_path.exists() {
        return Err("Program not found".into());
    }
    println!("Opening program: {:?}", path);
    let parent_path = program_path.parent().expect("Failed to get parent path");
    std::process::Command::new("explorer.exe")
        .arg(path)
        .current_dir(&parent_path)
        .spawn()
        .expect("failed to execute process");
    return Ok(());
}

fn start_new_program_using_vbs(path: &str) -> Result<(), String> {
    let program_path = std::path::Path::new(path);
    let working_dir = program_path
        .parent()
        .expect(format!("Failed to get parent path, Program: {}", path).as_str());
    let working_dir = bat_vbs::adjust_canonicalization(
        working_dir
            .canonicalize()
            .expect("Failed to canonicalize parent path"),
    );
    let temp_folder = std::env::temp_dir();
    let file_name = "$$_aiverything_open_program.vbs";
    let vbs_file_path = temp_folder.join(file_name);
    match bat_vbs::generate_open_file_vbs(path, vbs_file_path.to_owned()) {
        Ok(_) => {
            std::process::Command::new("cmd.exe")
                .creation_flags(0x08000000)
                .current_dir(&working_dir)
                .arg("/C")
                .arg(vbs_file_path)
                .spawn()
                .expect("failed to execute process");
            Ok(())
        }
        Err(e) => Err(e.to_string()),
    }
}

// fn start_new_program_using_winapi(target: &str) -> Result<(), String> {
//     println!("Open file with admin: {}", target);
//     unsafe {
//         let file_abs_path = format!("{}\0", target);
//         let operation = PCWSTR("open\0".encode_utf16().collect::<Vec<u16>>().as_ptr());
//         let file = PCWSTR(file_abs_path.encode_utf16().collect::<Vec<u16>>().as_ptr());

//         let result = ShellExecuteW(
//             None,
//             operation,
//             file,
//             PCWSTR(null_mut()),
//             PCWSTR(null_mut()),
//             SW_NORMAL,
//         );

//         let ret = result.0 as i128;
//         println!("open with admin return value: {}", ret);
//         if ret > 32 {
//             Ok(())
//         } else {
//             let error_code = GetLastError().0; // 获取具体错误代码
//             Err(format!(
//                 "Failed to start program with admin. Error code: {}",
//                 error_code
//             ))
//         }
//     }
// }
