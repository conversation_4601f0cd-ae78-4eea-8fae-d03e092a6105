[package]
name = "aiverything"
version = "0.2.5"
description = "Aiverything | Launcher to your everything."
authors = ["Aiverything"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.2.0", features = [] }

[dependencies]
tauri = { version = "2.5.1", features = ["tray-icon", "devtools"] }
serde = { version = "1.0.217", features = ["derive"] }
serde_json = "1.0.134"
tauri-plugin-shell = "2.2.1"
tauri-plugin-dialog = "2.2.1"
tauri-plugin-notification = "2.2.2"
tauri-plugin-process = "2.2.1"
tauri-plugin-prevent-default = "1.0.2"
reqwest = { version = "0.12.12", features = ["json"] }
lazy_static = "1.5.0"
window-vibrancy = "0.6.0"
rcgen = { version = "0.13.2", features = ["crypto", "aws_lc_rs"] }
local-encoding = "0.2.0"
config = "0.15.6"
ctrlc = "3.4.5"
schannel = "0.1.27"
windows = { version = "0.59", features = [
    "Win32_Foundation",
    "Win32_UI_Shell",
    "Win32_UI_WindowsAndMessaging",
    "Win32_Graphics_Dwm",
    "Win32_Globalization",
    "Win32_UI_Input_KeyboardAndMouse",
] }
raw-window-handle = "0.6.2"
libloading = "0.8.6"
encoding_rs = "0.8.35"
codepage = "0.1.2"
url = "2.5.4"
http = "1.2.0"
tauri-plugin-os = "2.2.1"
tauri-plugin-deep-link = "2.2.1"

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-global-shortcut = "2.2.0"
tauri-plugin-single-instance = { version = "2.2.3", features = ["deep-link"] }
tauri-plugin-updater = "2.7.1"
