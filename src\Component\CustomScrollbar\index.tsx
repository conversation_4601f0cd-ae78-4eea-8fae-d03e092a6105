import React, { useState, useRef, useEffect, useCallback } from "react";
import "./CustomScrollbar.css";

// TypeScript interfaces
interface CustomScrollbarProps {
  children: React.ReactNode;
  className?: string;
  normalWidth?: number;
  expandedWidth?: number;
  animationDuration?: number;
  trackColor?: string;
  thumbColor?: string;
  thumbHoverColor?: string;
  contentRef?: React.RefObject<HTMLDivElement>;
}

interface ScrollbarState {
  isHovered: boolean;
  isThumbHovered: boolean;
  isScrolling: boolean;
  scrollTop: number;
  scrollHeight: number;
  clientHeight: number;
  thumbHeight: number;
  thumbTop: number;
  showScrollbar: boolean;
}

interface ScrollbarMetrics {
  containerHeight: number;
  contentHeight: number;
  scrollTop: number;
  scrollRatio: number;
  thumbHeight: number;
  thumbTop: number;
  maxScrollTop: number;
}

const CustomScrollbar: React.FC<CustomScrollbarProps> = ({
  children,
  className = "",
  normalWidth = 6,
  expandedWidth = 12,
  animationDuration = 300,
  trackColor = "transparent",
  thumbColor = "rgba(100, 100, 100, 0.5)",
  thumbHoverColor = "rgba(100, 100, 100, 0.8)",
  contentRef: externalContentRef,
}) => {
  // State management
  const [scrollbarState, setScrollbarState] = useState<ScrollbarState>({
    isHovered: false,
    isThumbHovered: false,
    isScrolling: false,
    scrollTop: 0,
    scrollHeight: 0,
    clientHeight: 0,
    thumbHeight: 0,
    thumbTop: 0,
    showScrollbar: false,
  });

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const internalContentRef = useRef<HTMLDivElement>(null);
  const contentRef = externalContentRef || internalContentRef;
  const trackRef = useRef<HTMLDivElement>(null);
  const thumbRef = useRef<HTMLDivElement>(null);

  // Calculate scrollbar metrics
  const calculateMetrics = useCallback((): ScrollbarMetrics | null => {
    if (!contentRef.current) return null;

    const containerHeight = contentRef.current.clientHeight;
    const contentHeight = contentRef.current.scrollHeight;
    const scrollTop = contentRef.current.scrollTop;
    const maxScrollTop = contentHeight - containerHeight;

    if (maxScrollTop <= 0) {
      return {
        containerHeight,
        contentHeight,
        scrollTop: 0,
        scrollRatio: 0,
        thumbHeight: 0,
        thumbTop: 0,
        maxScrollTop: 0,
      };
    }

    const scrollRatio = scrollTop / maxScrollTop;

    // Calculate thumb height as a proportion of container height
    const thumbHeightRatio = containerHeight / contentHeight;
    const thumbHeight = Math.max(
      thumbHeightRatio * containerHeight,
      20 // Minimum thumb height for usability
    );

    // Calculate thumb position
    const maxThumbTop = containerHeight - thumbHeight;
    const thumbTop = Math.min(scrollRatio * maxThumbTop, maxThumbTop);

    return {
      containerHeight,
      contentHeight,
      scrollTop,
      scrollRatio,
      thumbHeight,
      thumbTop,
      maxScrollTop,
    };
  }, []);

  // Update scrollbar state based on metrics
  const updateScrollbarState = useCallback(() => {
    const metrics = calculateMetrics();
    if (!metrics) return;

    setScrollbarState((prev) => ({
      ...prev,
      scrollTop: metrics.scrollTop,
      scrollHeight: metrics.contentHeight,
      clientHeight: metrics.containerHeight,
      thumbHeight: metrics.thumbHeight,
      thumbTop: metrics.thumbTop,
      showScrollbar: metrics.maxScrollTop > 0,
    }));
  }, [calculateMetrics]);

  const handleTrackMouseEnter = useCallback(() => {
    setScrollbarState((prev) => ({ ...prev, isHovered: true }));
  }, []);

  const handleTrackMouseLeave = useCallback(() => {
    setScrollbarState((prev) => ({ ...prev, isHovered: false }));
  }, []);

  const handleThumbMouseEnter = useCallback(() => {
    setScrollbarState((prev) => ({
      ...prev,
      isHovered: true,
      isThumbHovered: true,
    }));
  }, []);

  const handleThumbMouseLeave = useCallback(() => {
    setScrollbarState((prev) => ({ ...prev, isThumbHovered: false }));
  }, []);

  // Drag functionality
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartY, setDragStartY] = useState(0);
  const [dragStartScrollTop, setDragStartScrollTop] = useState(0);

  const handleThumbMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      if (!contentRef.current) return;

      setIsDragging(true);
      setDragStartY(e.clientY);
      setDragStartScrollTop(contentRef.current.scrollTop);

      // Add global mouse event listeners
      const handleMouseMove = (e: MouseEvent) => {
        if (!contentRef.current) return;

        const deltaY = e.clientY - dragStartY;
        const metrics = calculateMetrics();
        if (!metrics) return;

        // Calculate new scroll position
        const thumbTravel = metrics.containerHeight - metrics.thumbHeight;
        const scrollTravel = metrics.maxScrollTop;
        const scrollDelta = (deltaY / thumbTravel) * scrollTravel;
        const newScrollTop = Math.max(
          0,
          Math.min(dragStartScrollTop + scrollDelta, metrics.maxScrollTop)
        );

        contentRef.current.scrollTop = newScrollTop;
      };

      const handleMouseUp = () => {
        setIsDragging(false);
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };

      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    },
    [dragStartY, dragStartScrollTop, calculateMetrics]
  );

  const handleTrackClick = useCallback(
    (e: React.MouseEvent) => {
      if (!contentRef.current || !trackRef.current) return;

      const trackRect = trackRef.current.getBoundingClientRect();
      const clickY = e.clientY - trackRect.top;
      const metrics = calculateMetrics();
      if (!metrics) return;

      // Calculate target scroll position
      const clickRatio = clickY / metrics.containerHeight;
      const targetScrollTop = clickRatio * metrics.maxScrollTop;

      // Smooth scroll to target position
      contentRef.current.scrollTo({
        top: targetScrollTop,
        behavior: "smooth",
      });
    },
    [calculateMetrics]
  );

  // Scroll timeout ref for managing scroll state
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleScroll = useCallback(() => {
    setScrollbarState((prev) => ({ ...prev, isScrolling: true }));
    updateScrollbarState();

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Reset scrolling state after a delay
    scrollTimeoutRef.current = setTimeout(() => {
      setScrollbarState((prev) => ({ ...prev, isScrolling: false }));
    }, 150);
  }, [updateScrollbarState]);

  // Throttled scroll handler for better performance
  const throttledScrollHandler = useCallback(() => {
    let ticking = false;

    return () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };
  }, [handleScroll]);

  // Set up event listeners and initial state
  useEffect(() => {
    const contentElement = contentRef.current;
    if (!contentElement) return;

    // Initial calculation
    updateScrollbarState();

    // Create throttled scroll handler
    const throttledHandler = throttledScrollHandler();

    // Add scroll listener with throttling
    contentElement.addEventListener("scroll", throttledHandler, {
      passive: true,
    });

    // Add resize observer for content changes
    const resizeObserver = new ResizeObserver(() => {
      updateScrollbarState();
    });
    resizeObserver.observe(contentElement);

    return () => {
      contentElement.removeEventListener("scroll", throttledHandler);
      resizeObserver.disconnect();
    };
  }, [throttledScrollHandler, updateScrollbarState]);

  // CSS custom properties for dynamic styling
  const cssProperties = {
    "--scrollbar-normal-width": `${normalWidth}px`,
    "--scrollbar-expanded-width": `${expandedWidth}px`,
    "--scrollbar-animation-duration": `${animationDuration}ms`,
    "--scrollbar-track-color": trackColor,
    "--scrollbar-thumb-color": thumbColor,
    "--scrollbar-thumb-hover-color": thumbHoverColor,
  } as React.CSSProperties;

  const currentWidth =
    scrollbarState.isHovered || isDragging ? expandedWidth : normalWidth;

  return (
    <div
      ref={containerRef}
      className={`custom-scrollbar-container ${className}`}
      style={cssProperties}
    >
      {/* Content with hidden native scrollbar */}
      <div ref={contentRef} className="custom-scrollbar-content">
        {children}
      </div>

      {/* Custom scrollbar track and thumb */}
      {scrollbarState.showScrollbar && (
        <div
          ref={trackRef}
          className="custom-scrollbar-track"
          style={{
            width: `${currentWidth}px`,
            backgroundColor: trackColor,
          }}
          onMouseEnter={handleTrackMouseEnter}
          onMouseLeave={handleTrackMouseLeave}
          onClick={handleTrackClick}
        >
          <div
            ref={thumbRef}
            className={`custom-scrollbar-thumb ${
              scrollbarState.isThumbHovered ? "thumb-hovered" : ""
            } ${isDragging ? "thumb-dragging" : ""}`}
            style={{
              height: `${scrollbarState.thumbHeight}px`,
              top: `${scrollbarState.thumbTop}px`,
              backgroundColor:
                scrollbarState.isThumbHovered || isDragging
                  ? thumbHoverColor
                  : scrollbarState.isHovered
                  ? "rgba(100, 100, 100, 0.6)"
                  : thumbColor,
              cursor: isDragging ? "grabbing" : "grab",
              transform: scrollbarState.isThumbHovered
                ? "scale(1.05)"
                : "scale(1)",
              boxShadow: scrollbarState.isThumbHovered
                ? "0 2px 8px rgba(0, 0, 0, 0.2)"
                : "none",
            }}
            onMouseEnter={handleThumbMouseEnter}
            onMouseLeave={handleThumbMouseLeave}
            onMouseDown={handleThumbMouseDown}
          />
        </div>
      )}
    </div>
  );
};

export default CustomScrollbar;
